/* Contact Page Styles */
.contact-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(20px, 5vw, 40px);
}

/* Hero Section */
.contact-hero {
  background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
  color: white;
  padding: clamp(60px, 12vw, 100px) 0;
  text-align: center;
}

.hero-content h1 {
  font-size: clamp(2.5rem, 6vw, 3.5rem);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-content p {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  opacity: 0.95;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Content */
.contact-content {
  padding: clamp(60px, 10vw, 100px) 0;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: clamp(3rem, 6vw, 5rem);
  align-items: start;
}

/* Contact Information */
.contact-info h2 {
  font-size: clamp(1.8rem, 3.5vw, 2.2rem);
  color: #2c3e50;
  margin-bottom: 2rem;
  font-weight: 600;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
}

.info-icon {
  font-size: 1.5rem;
  background: linear-gradient(135deg, #e67e22, #f39c12);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-details h3 {
  font-size: clamp(1.1rem, 2vw, 1.2rem);
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.info-details p {
  color: #666;
  font-size: clamp(0.95rem, 1.8vw, 1rem);
  line-height: 1.5;
  margin: 0;
}

/* Hours Section */
.hours-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
}

.hours-section h3 {
  font-size: clamp(1.2rem, 2.2vw, 1.3rem);
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.hours-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.hour-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid #eee;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
}

.hour-item:last-child {
  border-bottom: none;
}

.hour-item span:first-child {
  color: #2c3e50;
  font-weight: 500;
}

.hour-item span:last-child {
  color: #e67e22;
  font-weight: 600;
}

/* Social Section */
.social-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.social-section h3 {
  font-size: clamp(1.2rem, 2.2vw, 1.3rem);
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  text-decoration: none;
  color: #2c3e50;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: linear-gradient(135deg, #e67e22, #f39c12);
  color: white;
  transform: translateX(5px);
}

/* Contact Form */
.contact-form-section h2 {
  font-size: clamp(1.8rem, 3.5vw, 2.2rem);
  color: #2c3e50;
  margin-bottom: 2rem;
  font-weight: 600;
}

.success-message {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  text-align: center;
}

.success-message p {
  margin: 0;
  font-size: clamp(0.95rem, 1.8vw, 1rem);
}

.contact-form {
  background: white;
  padding: clamp(2rem, 4vw, 2.5rem);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: clamp(0.95rem, 1.8vw, 1rem);
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: clamp(0.8rem, 1.5vw, 1rem);
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #e67e22;
  box-shadow: 0 0 0 3px rgba(230, 126, 34, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  background: linear-gradient(135deg, #e67e22, #f39c12);
  color: white;
  border: none;
  padding: clamp(1rem, 2vw, 1.2rem) clamp(2rem, 4vw, 3rem);
  border-radius: 8px;
  font-size: clamp(1rem, 1.8vw, 1.1rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(230, 126, 34, 0.3);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Map Section */
.map-section {
  background: #f8f9fa;
  padding: clamp(60px, 10vw, 100px) 0;
}

.map-section h2 {
  text-align: center;
  font-size: clamp(1.8rem, 3.5vw, 2.2rem);
  color: #2c3e50;
  margin-bottom: 3rem;
  font-weight: 600;
}

.map-placeholder {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-content {
  text-align: center;
  color: #666;
}

.map-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.map-content p {
  font-size: clamp(1rem, 2vw, 1.1rem);
  margin: 0.5rem 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .social-link {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hours-list {
    gap: 0.5rem;
  }

  .hour-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }

  .social-links {
    flex-direction: column;
  }
}
