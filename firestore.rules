rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    
    // Admin users collection - only authenticated admins can access their own data
    match /admins/{adminId} {
      allow read, write: if request.auth != null && request.auth.uid == adminId;
    }
    
    // Products collection - read for everyone, write only for authenticated admins
    match /products/{productId} {
      allow read: if true; // Public read access for menu display
      allow write: if request.auth != null && isAdmin();
    }
    
    // Categories collection - read for everyone, write only for authenticated admins
    match /categories/{categoryId} {
      allow read: if true; // Public read access for menu categories
      allow write: if request.auth != null && isAdmin();
    }
    
    // Orders collection - write for everyone (placing orders), read/update only for authenticated admins
    match /orders/{orderId} {
      allow create: if true; // Anyone can place an order
      allow read, update, delete: if request.auth != null && isAdmin();
    }
    
    // Settings collection - read for everyone (business info), write only for authenticated admins
    match /settings/{settingId} {
      allow read: if true; // Public read access for business information
      allow write: if request.auth != null && isAdmin();
    }
    
    // Order counter - only authenticated admins can access
    match /counters/{counterId} {
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // Analytics collection (optional) - only authenticated admins
    match /analytics/{analyticsId} {
      allow read, write: if request.auth != null && isAdmin();
    }
    
    // Helper function to check if user is an admin
    function isAdmin() {
      return exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    // Helper function to validate order data
    function isValidOrder(order) {
      return order.keys().hasAll(['customerName', 'customerPhone', 'items', 'total', 'status']) &&
             order.customerName is string &&
             order.customerPhone is string &&
             order.items is list &&
             order.total is number &&
             order.status is string &&
             order.total >= 0;
    }
    
    // Helper function to validate product data
    function isValidProduct(product) {
      return product.keys().hasAll(['name', 'price', 'category', 'available']) &&
             product.name is string &&
             product.price is number &&
             product.category is string &&
             product.available is bool &&
             product.price >= 0;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
