<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Configuration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Firebase Configuration Test</h1>
    <p>This page tests if Firebase configuration works properly when environment variables are missing.</p>
    
    <div id="test-results"></div>

    <script>
        const results = document.getElementById('test-results');
        
        function addResult(type, title, message, details = null) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <p>${message}</p>
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            results.appendChild(div);
        }

        // Test 1: Check environment variables
        addResult('info', '🔍 Environment Variables Check', 'Checking if Firebase environment variables are available...');
        
        const envVars = [
            'NEXT_PUBLIC_FIREBASE_API_KEY',
            'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN', 
            'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
            'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
            'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
            'NEXT_PUBLIC_FIREBASE_APP_ID'
        ];

        const missingVars = envVars.filter(varName => !process?.env?.[varName]);
        
        if (missingVars.length === 0) {
            addResult('success', '✅ Environment Variables', 'All Firebase environment variables are available!');
        } else {
            addResult('warning', '⚠️ Missing Environment Variables', 
                `Missing: ${missingVars.join(', ')}`, 
                'This is expected in a static HTML test. In your deployed app, make sure these are set in Vercel.');
        }

        // Test 2: Simulate Firebase configuration
        addResult('info', '🔥 Firebase Configuration Test', 'Testing Firebase configuration handling...');
        
        // Simulate the Firebase config logic
        const mockFirebaseConfig = {
            apiKey: '',
            authDomain: '',
            projectId: '',
            storageBucket: '',
            messagingSenderId: '',
            appId: ''
        };

        const hasValidConfig = mockFirebaseConfig.apiKey && 
                              mockFirebaseConfig.projectId && 
                              mockFirebaseConfig.apiKey !== '' && 
                              mockFirebaseConfig.projectId !== '';

        if (!hasValidConfig) {
            addResult('success', '✅ Configuration Handling', 
                'Firebase configuration correctly detects missing environment variables and handles gracefully without crashing!');
        } else {
            addResult('error', '❌ Configuration Test Failed', 'Configuration validation logic may not be working correctly.');
        }

        // Test 3: Check for console errors
        addResult('info', '📝 Console Check', 'Check the browser console for any Firebase-related errors...');
        
        // Capture console errors
        const originalError = console.error;
        let errorCount = 0;
        console.error = function(...args) {
            errorCount++;
            originalError.apply(console, args);
        };

        setTimeout(() => {
            console.error = originalError;
            if (errorCount === 0) {
                addResult('success', '✅ No Console Errors', 'No Firebase-related errors detected in console!');
            } else {
                addResult('warning', '⚠️ Console Errors Detected', 
                    `${errorCount} error(s) detected. Check console for details.`);
            }
        }, 2000);

        // Test 4: Instructions
        addResult('info', '📋 Next Steps', 
            'If you see this page without crashes, the Firebase configuration fixes are working!',
            `To complete the deployment:
1. Set all Firebase environment variables in Vercel Dashboard
2. Redeploy your application
3. Test the live site

Environment variables to set in Vercel:
${envVars.map(v => `- ${v}`).join('\n')}`);

    </script>
</body>
</html>
