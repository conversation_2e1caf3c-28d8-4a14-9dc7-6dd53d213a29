/* CSS Variables for Theme Consistency */
:root {
  --admin-primary: #ff9f1c;
  --admin-primary-light: #ffb54f;
  --admin-primary-dark: #e67e22;
  --admin-secondary: #2c3e50;
  --admin-background: #fff8f0;
  --admin-white: #ffffff;
  --admin-gray-light: #f8f9fa;
  --admin-gray: #666666;
  --admin-gray-dark: #333333;
  --admin-border: #e9ecef;
  --admin-shadow: 0 4px 12px rgba(255, 159, 28, 0.15);
  --admin-shadow-hover: 0 8px 25px rgba(255, 159, 28, 0.25);
  --admin-danger: #dc3545;
  --admin-success: #28a745;
  --admin-warning: #ffc107;
  --admin-info: #17a2b8;
  --admin-gradient: linear-gradient(135deg, var(--admin-background) 0%, #fff 50%, var(--admin-background) 100%);
  --admin-card-shadow: 0 8px 32px rgba(255, 159, 28, 0.12);
  --admin-card-shadow-hover: 0 12px 40px rgba(255, 159, 28, 0.18);
}

/* Admin Container */
.admin-container {
  min-height: 100vh;
  height: 100vh;
  width: 100vw;
  background: var(--admin-gradient);
  padding: 0;
  font-family: 'Arial', sans-serif;
  position: relative;
  overflow-y: auto;
}

.admin-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 159, 28, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(44, 62, 80, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.admin-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: var(--admin-card-shadow);
  padding: 2rem 3rem;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(255, 159, 28, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  border-radius: 0 0 20px 20px;
}

.admin-header h1 {
  color: var(--admin-secondary);
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-secondary) 0%, var(--admin-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.admin-header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  border-radius: 2px;
}

.logout-btn {
  background: linear-gradient(135deg, var(--admin-danger) 0%, #e74c3c 100%);
  color: var(--admin-white);
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
  position: relative;
  overflow: hidden;
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.logout-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
  background: linear-gradient(135deg, #e74c3c 0%, var(--admin-danger) 100%);
}

.logout-btn:hover::before {
  left: 100%;
}

/* Admin Content */
.admin-content {
  padding: 2rem 3rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Admin Tabs */
.admin-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px;
  box-shadow: var(--admin-card-shadow);
  border: 1px solid rgba(255, 159, 28, 0.15);
}

.tab-btn {
  flex: 1;
  background: transparent;
  border: none;
  padding: 18px 28px;
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-gray);
  cursor: pointer;
  border-radius: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tab-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 15px;
  transform: scale(0.8);
}

.tab-btn:hover {
  color: var(--admin-primary);
  transform: translateY(-2px);
}

.tab-btn:hover::before {
  opacity: 0.1;
  transform: scale(1);
}

.tab-btn.active {
  color: var(--admin-white);
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  box-shadow: 0 4px 15px rgba(255, 159, 28, 0.3);
  transform: translateY(-1px);
}

.tab-btn.active::before {
  opacity: 0;
}

/* Section Tabs */
.section-tabs {
  display: flex;
  gap: 0;
  margin: 0 0 2rem 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 15px;
  padding: 6px;
  box-shadow: 0 4px 20px rgba(255, 159, 28, 0.1);
  border: 1px solid rgba(255, 159, 28, 0.1);
}

.section-tab {
  flex: 1;
  background: none;
  border: none;
  padding: 14px 24px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--admin-gray);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.section-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
  z-index: -1;
}

.section-tab:hover {
  color: var(--admin-primary);
  transform: translateY(-1px);
}

.section-tab:hover::before {
  opacity: 0.1;
}

.section-tab.active {
  color: var(--admin-white);
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  box-shadow: 0 3px 12px rgba(255, 159, 28, 0.25);
}

.section-tab.active::before {
  opacity: 0;
}

/* Login Form */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: var(--admin-gradient);
  padding: 2rem;
  position: relative;
}

.login-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 70%, rgba(255, 159, 28, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(44, 62, 80, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 25px;
  box-shadow: var(--admin-card-shadow-hover);
  width: 100%;
  max-width: 480px;
  border: 1px solid rgba(255, 159, 28, 0.15);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 50%, var(--admin-primary) 100%);
  border-radius: 25px 25px 0 0;
}

.login-form::after {
  content: '';
  position: absolute;
  top: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.1) 0%, rgba(255, 181, 79, 0.1) 100%);
  border-radius: 50%;
  z-index: -1;
}

.login-form h2 {
  text-align: center;
  margin-bottom: 2.5rem;
  color: var(--admin-secondary);
  font-size: 2.2rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-secondary) 0%, var(--admin-primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.login-form h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  border-radius: 2px;
}



/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--admin-secondary);
  font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid rgba(255, 159, 28, 0.2);
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(255, 159, 28, 0.15);
  background: var(--admin-white);
  transform: translateY(-1px);
}

.form-group small {
  display: block;
  margin-top: 6px;
  color: var(--admin-gray);
  font-size: 0.85rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: normal !important;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  transform: scale(1.2);
  accent-color: var(--admin-primary);
}

/* Buttons */
.login-btn, .submit-btn, .save-btn, .confirm-btn {
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  color: var(--admin-white);
  border: none;
  padding: 14px 28px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  box-shadow: 0 4px 15px rgba(255, 159, 28, 0.3);
  position: relative;
  overflow: hidden;
}

.login-btn::before, .submit-btn::before, .save-btn::before, .confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover, .submit-btn:hover, .save-btn:hover, .confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 159, 28, 0.4);
  background: linear-gradient(135deg, var(--admin-primary-light) 0%, var(--admin-primary) 100%);
}

.login-btn:hover::before, .submit-btn:hover::before, .save-btn:hover::before, .confirm-btn:hover::before {
  left: 100%;
}

.cancel-btn {
  background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-gray-dark) 100%);
  color: var(--admin-white);
  border: none;
  padding: 12px 24px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(102, 102, 102, 0.3);
}

.cancel-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 102, 102, 0.4);
  background: linear-gradient(135deg, var(--admin-gray-dark) 0%, var(--admin-gray) 100%);
}

.edit-btn {
  background: linear-gradient(135deg, var(--admin-warning) 0%, #f39c12 100%);
  color: var(--admin-white);
  border: none;
  padding: 10px 18px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
}

.edit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
  background: linear-gradient(135deg, #f39c12 0%, var(--admin-warning) 100%);
}

.delete-btn {
  background: linear-gradient(135deg, var(--admin-danger) 0%, #c0392b 100%);
  color: var(--admin-white);
  border: none;
  padding: 10px 18px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(220, 53, 69, 0.3);
}

.delete-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
  background: linear-gradient(135deg, #c0392b 0%, var(--admin-danger) 100%);
}

.complete-btn {
  background: linear-gradient(135deg, var(--admin-success) 0%, #27ae60 100%);
  color: var(--admin-white);
  border: none;
  padding: 10px 18px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.complete-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #27ae60 0%, var(--admin-success) 100%);
}

.complete-btn.small {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.cancel-btn.small {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.view-btn {
  background: linear-gradient(135deg, var(--admin-info) 0%, #138496 100%);
  color: var(--admin-white);
  border: none;
  padding: 8px 14px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  margin-right: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.view-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(23, 162, 184, 0.4);
  background: linear-gradient(135deg, #138496 0%, var(--admin-info) 100%);
}

.toggle-btn {
  border: none;
  padding: 10px 18px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.toggle-btn.enable {
  background: linear-gradient(135deg, var(--admin-success) 0%, #27ae60 100%);
  color: var(--admin-white);
  box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.toggle-btn.enable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.toggle-btn.disable {
  background: linear-gradient(135deg, var(--admin-warning) 0%, #f39c12 100%);
  color: var(--admin-white);
  box-shadow: 0 3px 10px rgba(255, 193, 7, 0.3);
}

.toggle-btn.disable:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
  background: linear-gradient(135deg, #f39c12 0%, var(--admin-warning) 100%);
}

.add-btn {
  background: linear-gradient(135deg, var(--admin-success) 0%, #27ae60 100%);
  color: var(--admin-white);
  border: none;
  padding: 12px 24px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  margin-left: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 3px 12px rgba(40, 167, 69, 0.3);
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 18px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #27ae60 0%, var(--admin-success) 100%);
}

.clear-filters-btn {
  background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-gray-dark) 100%);
  color: var(--admin-white);
  border: none;
  padding: 10px 18px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(102, 102, 102, 0.3);
}

.clear-filters-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 102, 102, 0.4);
  background: linear-gradient(135deg, var(--admin-gray-dark) 0%, var(--admin-gray) 100%);
}

.delete-category-btn {
  background: linear-gradient(135deg, var(--admin-danger) 0%, #c0392b 100%);
  color: var(--admin-white);
  border: none;
  padding: 8px 14px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.delete-category-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 3px 12px rgba(220, 53, 69, 0.4);
  background: linear-gradient(135deg, #c0392b 0%, var(--admin-danger) 100%);
}

.delete-category-btn:disabled {
  background: linear-gradient(135deg, #ccc 0%, #bbb 100%);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Error Message */
.error-message {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(244, 67, 54, 0.1) 100%);
  color: var(--admin-danger);
  padding: 14px 18px;
  border-radius: 15px;
  margin-bottom: 18px;
  border: 1px solid rgba(220, 53, 69, 0.2);
  font-weight: 500;
  position: relative;
  backdrop-filter: blur(5px);
}

.error-message::before {
  content: '⚠️';
  position: absolute;
  left: 18px;
  top: 50%;
  transform: translateY(-50%);
}

.error-message {
  padding-left: 50px;
}

/* Save Status */
.save-status {
  padding: 14px 20px;
  border-radius: 15px;
  margin-bottom: 24px;
  text-align: center;
  font-weight: 600;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.save-status::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.save-status:hover::before {
  left: 100%;
}

.save-status.saving {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.15) 0%, rgba(239, 108, 0, 0.15) 100%);
  color: var(--admin-warning);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.save-status.saved {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.15) 0%, rgba(46, 125, 50, 0.15) 100%);
  color: var(--admin-success);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.save-status.error {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.15) 0%, rgba(244, 67, 54, 0.15) 100%);
  color: var(--admin-danger);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Orders Management */
.orders-management {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--admin-card-shadow);
  border: 1px solid rgba(255, 159, 28, 0.1);
  position: relative;
  overflow: hidden;
}

.orders-management::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  border-radius: 20px 20px 0 0;
}

.orders-queue h3, .orders-history h3 {
  margin-bottom: 1.5rem;
  color: var(--admin-secondary);
  font-size: 1.5rem;
  font-weight: 600;
  position: relative;
  padding-left: 2rem;
}

.orders-queue h3::before, .orders-history h3::before {
  content: '📋';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}

.no-orders {
  text-align: center;
  color: var(--admin-gray);
  font-style: italic;
  padding: 3rem;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.05) 0%, rgba(255, 181, 79, 0.05) 100%);
  border-radius: 15px;
  border: 2px dashed rgba(255, 159, 28, 0.2);
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 1.5rem;
}

.order-card {
  border: 1px solid rgba(255, 159, 28, 0.15);
  border-radius: 18px;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--admin-card-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--admin-card-shadow-hover);
  border-color: rgba(255, 159, 28, 0.25);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.order-id {
  font-weight: 700;
  color: var(--admin-secondary);
  font-size: 1.1rem;
}

.table-number {
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.15) 0%, rgba(255, 181, 79, 0.15) 100%);
  color: var(--admin-primary);
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(255, 159, 28, 0.2);
}

.order-status {
  color: var(--admin-white);
  padding: 8px 14px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.order-time {
  color: var(--admin-gray);
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-time::before {
  content: '🕒';
  font-size: 0.8rem;
}

.order-items {
  margin-bottom: 1.2rem;
  background: rgba(255, 159, 28, 0.05);
  border-radius: 12px;
  padding: 1rem;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid rgba(255, 159, 28, 0.1);
  font-size: 0.9rem;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-name {
  font-weight: 500;
  color: var(--admin-secondary);
}

.order-item-quantity {
  color: var(--admin-primary);
  font-weight: 600;
  background: rgba(255, 159, 28, 0.1);
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
}

.order-total {
  text-align: right;
  margin-bottom: 1.2rem;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--admin-secondary);
  padding: 0.8rem;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.1) 0%, rgba(255, 181, 79, 0.1) 100%);
  border-radius: 12px;
  border: 1px solid rgba(255, 159, 28, 0.2);
}

.order-actions {
  display: flex;
  gap: 0.8rem;
  flex-wrap: wrap;
}

.order-actions button {
  flex: 1;
  padding: 10px 16px;
  font-size: 0.9rem;
  min-width: 120px;
}

/* Filters */
.filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.2rem;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 18px;
  border: 1px solid rgba(255, 159, 28, 0.15);
  box-shadow: var(--admin-card-shadow);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-group label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--admin-secondary);
  font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
  padding: 12px 14px;
  border: 2px solid rgba(255, 159, 28, 0.2);
  border-radius: 12px;
  font-size: 0.95rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(255, 159, 28, 0.15);
  background: var(--admin-white);
}

/* Orders Table */
.orders-table {
  overflow-x: auto;
  border-radius: 18px;
  box-shadow: var(--admin-card-shadow);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.orders-table table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
  border-radius: 18px;
  overflow: hidden;
}

.orders-table th,
.orders-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(255, 159, 28, 0.1);
}

.orders-table th {
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.1) 0%, rgba(255, 181, 79, 0.1) 100%);
  font-weight: 600;
  color: var(--admin-secondary);
  font-size: 0.95rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.orders-table tr:hover {
  background: rgba(255, 159, 28, 0.05);
}

.status-badge {
  color: var(--admin-white);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Product Management */
.product-management {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--admin-card-shadow);
  border: 1px solid rgba(255, 159, 28, 0.1);
  position: relative;
  overflow: hidden;
}

.product-management::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  border-radius: 20px 20px 0 0;
}

.product-form {
  max-width: 700px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
  padding: 2rem;
  border-radius: 18px;
  border: 1px solid rgba(255, 159, 28, 0.15);
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.form-actions button {
  flex: 1;
  min-width: 140px;
}

.no-products {
  text-align: center;
  color: var(--admin-gray);
  font-style: italic;
  padding: 3rem;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.05) 0%, rgba(255, 181, 79, 0.05) 100%);
  border-radius: 15px;
  border: 2px dashed rgba(255, 159, 28, 0.2);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.product-card {
  border: 1px solid rgba(255, 159, 28, 0.15);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--admin-card-shadow);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
}

.product-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--admin-card-shadow-hover);
  border-color: rgba(255, 159, 28, 0.3);
}

/* Product Image Container */
.product-image-container {
  position: relative;
  height: 220px;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.05) 0%, rgba(255, 181, 79, 0.05) 100%);
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--admin-gray);
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.08) 0%, rgba(255, 181, 79, 0.08) 100%);
}

.product-placeholder span {
  font-size: 3rem;
  margin-bottom: 0.5rem;
  opacity: 0.6;
}

.product-placeholder p {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.7;
}

.product-status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.product-status-badge.available {
  background: rgba(40, 167, 69, 0.9);
  color: var(--admin-white);
}

.product-status-badge.unavailable {
  background: rgba(220, 53, 69, 0.9);
  color: var(--admin-white);
}

/* Product Info */
.product-info {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--admin-secondary);
  margin: 0 0 0.8rem 0;
  line-height: 1.3;
}

.product-description {
  color: var(--admin-gray);
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.product-category-tag {
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.15) 0%, rgba(255, 181, 79, 0.15) 100%);
  color: var(--admin-primary);
  padding: 6px 14px;
  border-radius: 15px;
  font-size: 0.85rem;
  font-weight: 600;
  border: 1px solid rgba(255, 159, 28, 0.2);
}

.product-price {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--admin-primary);
  background: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 0.8rem;
  padding: 0 1.5rem 1.5rem;
  flex-wrap: wrap;
}

.product-actions button {
  flex: 1;
  min-width: 100px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.edit-btn {
  background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
  color: white;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.toggle-btn.enable {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.toggle-btn.enable:hover {
  background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.toggle-btn.disable {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: white;
}

.toggle-btn.disable:hover {
  background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.delete-btn {
  background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
  color: white;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #e83e8c 0%, #dc3545 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.product-info h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.product-description {
  color: #666;
  margin-bottom: 10px;
  font-size: 14px;
}

.product-category,
.product-price,
.product-status {
  margin-bottom: 8px;
  font-size: 14px;
}

.product-price {
  font-weight: bold;
  color: #2196f3;
  font-size: 16px;
}

.product-actions {
  display: flex;
  gap: 8px;
  margin-top: 15px;
  flex-wrap: wrap;
}

.product-actions button {
  flex: 1;
  min-width: 80px;
}

/* Categories */
.categories-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-category {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.category-form {
  display: flex;
  align-items: flex-end;
  gap: 10px;
}

.category-form .form-group {
  flex: 1;
  margin-bottom: 0;
}

.categories-list h4 {
  margin-bottom: 15px;
  color: #333;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-name {
  font-weight: bold;
  color: #333;
}

.product-count {
  color: #666;
  font-size: 14px;
}

/* Settings Panel */
.settings-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-form {
  max-width: 600px;
  margin: 0 auto;
}

.security-note {
  margin-top: 30px;
  padding: 15px;
  background-color: #fff3e0;
  border-radius: 6px;
  border-left: 4px solid #ff9800;
}

.security-note h4 {
  margin: 0 0 10px 0;
  color: #ef6c00;
}

.security-note p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* Currency Settings */
.currency-selection {
  margin-bottom: 30px;
}

.currency-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.currency-option {
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
  background: white;
}

.currency-option:hover {
  border-color: #2196f3;
  background-color: #f8f9fa;
}

.currency-option.selected {
  border-color: #2196f3;
  background-color: #e3f2fd;
}

.currency-symbol {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 5px;
}

.currency-code {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.currency-name {
  display: block;
  font-size: 14px;
  color: #666;
}

.pricing-form {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.pricing-preview {
  margin-top: 30px;
  padding: 20px;
  background-color: #e8f5e8;
  border-radius: 8px;
  border: 1px solid #4caf50;
}

.preview-calculation {
  max-width: 300px;
}

.preview-line {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
}

.preview-line:last-child {
  border-bottom: none;
}

.preview-line.total {
  border-top: 2px solid #333;
  margin-top: 10px;
  padding-top: 10px;
  font-size: 16px;
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.close-btn:hover {
  background-color: #e0e0e0;
}

.modal-body {
  padding: 20px;
}

.modal-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 0 0 8px 8px;
}

.modal-actions button {
  width: auto;
  min-width: 100px;
}

.confirmation-modal {
  max-width: 400px;
}

.confirmation-modal .modal-body {
  text-align: center;
  padding: 30px 20px;
}

.confirmation-modal .modal-body p {
  margin: 0;
  font-size: 16px;
  color: #333;
}

/* Order Details Modal */
.order-info {
  margin-bottom: 20px;
}

.order-info p {
  margin: 8px 0;
  font-size: 14px;
}

.order-items-detail h4 {
  margin-bottom: 15px;
  color: #333;
}

.item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.item-detail:last-child {
  border-bottom: none;
}

.order-total-detail {
  text-align: right;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 2px solid #e0e0e0;
  font-size: 18px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    padding: 0.5rem;
  }

  .admin-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1.5rem 2rem;
    border-radius: 0 0 15px 15px;
  }

  .admin-header h1 {
    font-size: 2rem;
  }

  .admin-content {
    padding: 1rem 1.5rem;
  }

  .admin-tabs {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .tab-btn {
    padding: 14px 20px;
    font-size: 0.95rem;
  }

  .section-tabs {
    flex-direction: column;
    gap: 0.3rem;
    padding: 0.4rem;
  }

  .section-tab {
    padding: 12px 18px;
    font-size: 0.9rem;
  }

  .orders-grid,
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .filters {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }

  .currency-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .modal-content {
    width: 95%;
    margin: 0.5rem;
    border-radius: 15px;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .modal-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .product-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .order-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .category-form {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .category-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.8rem;
  }

  .login-form {
    padding: 2rem;
    max-width: 400px;
    border-radius: 20px;
  }

  .login-form h2 {
    font-size: 1.8rem;
  }

  .orders-table th,
  .orders-table td {
    padding: 0.8rem 0.5rem;
    font-size: 0.9rem;
  }

  .order-card,
  .product-card {
    padding: 1.2rem;
  }

  .order-actions button,
  .product-actions button {
    min-width: auto;
    padding: 8px 14px;
    font-size: 0.85rem;
  }

  /* Product Cards Mobile */
  .products-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .product-image-container {
    height: 180px;
  }

  .product-actions {
    flex-direction: column;
    gap: 0.8rem;
  }

  .product-actions button {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .admin-header {
    padding: 1rem;
  }

  .admin-header h1 {
    font-size: 1.6rem;
  }

  .admin-content {
    padding: 0.8rem 1rem;
  }

  .login-form {
    padding: 1.5rem;
    max-width: 350px;
  }

  .login-form h2 {
    font-size: 1.6rem;
  }

  .tab-btn {
    padding: 12px 16px;
    font-size: 0.9rem;
  }

  .section-tab {
    padding: 10px 14px;
    font-size: 0.85rem;
  }

  .orders-grid,
  .products-grid {
    gap: 0.8rem;
  }

  .order-card,
  .product-card {
    padding: 1rem;
  }

  .product-image-container {
    height: 160px;
  }

  .product-info {
    padding: 1rem;
  }

  .product-name {
    font-size: 1.1rem;
  }

  .product-price {
    font-size: 1.2rem;
  }

  .product-actions button {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 12px 14px;
    font-size: 0.95rem;
  }

  .login-btn,
  .submit-btn,
  .save-btn,
  .confirm-btn {
    padding: 12px 20px;
    font-size: 0.95rem;
  }
}

/* Firebase Info Section */
.firebase-info {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(139, 195, 74, 0.1) 100%);
  border-radius: 12px;
  font-size: 14px;
  border: 1px solid rgba(76, 175, 80, 0.2);
  color: var(--admin-gray-dark);
}

.firebase-info p {
  margin: 0.5rem 0;
}

.firebase-info strong {
  color: #4caf50;
}

/* Admin User Info */
.admin-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-welcome {
  color: var(--admin-gray-dark);
  font-size: 0.9rem;
  font-weight: 500;
}

@media (max-width: 768px) {
  .admin-user-info {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-end;
  }

  .user-welcome {
    font-size: 0.8rem;
    text-align: right;
  }
}

/* Loading State */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  padding: 2rem;
}

.admin-loading h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.admin-loading p {
  color: #7f8c8d;
  font-size: clamp(1rem, 2.5vw, 1.1rem);
}

/* Setup State */
.admin-setup-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.admin-setup-container h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.admin-setup-container p {
  color: #7f8c8d;
  margin-bottom: 2rem;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .admin-setup-container {
    padding: 1rem;
  }
}

/* Error State */
.admin-error {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  background: var(--admin-white);
  border-radius: 12px;
  box-shadow: var(--admin-card-shadow);
  border-left: 5px solid var(--admin-warning);
}

.admin-error h2 {
  color: var(--admin-warning);
  margin-bottom: 1rem;
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.admin-error p {
  color: var(--admin-gray);
  margin-bottom: 1rem;
  font-size: clamp(0.9rem, 2.5vw, 1rem);
  line-height: 1.6;
}

.admin-error ol {
  text-align: left;
  margin: 1.5rem 0;
  padding-left: 1.5rem;
}

.admin-error li {
  color: var(--admin-gray-dark);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.admin-error code {
  background: var(--admin-gray-light);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
  color: var(--admin-secondary);
}

@media (max-width: 768px) {
  .admin-error {
    padding: 1rem;
    margin: 1rem;
  }
}

/* Order History Styles */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.clear-history-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-history-btn:hover {
  background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.danger-btn {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

.danger-btn:hover {
  background: linear-gradient(135deg, #c82333 0%, #dc3545 100%) !important;
}

.password-input {
  margin: 20px 0;
}

.password-input label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.password-input input {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 5px;
}

.password-input input:focus {
  outline: none;
  border-color: #ff9f1c;
  box-shadow: 0 0 0 3px rgba(255, 159, 28, 0.1);
}

.password-input small {
  color: #666;
  font-size: 12px;
}

@media (max-width: 768px) {
  .history-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .clear-history-btn {
    width: 100%;
  }
}
