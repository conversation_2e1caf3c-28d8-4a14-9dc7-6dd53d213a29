/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease-in-out;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.notification-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.notification-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Success Notification */
.notification-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.notification-success .notification-close:hover {
  background-color: rgba(21, 87, 36, 0.1);
}

/* Error Notification */
.notification-error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.notification-error .notification-close:hover {
  background-color: rgba(114, 28, 36, 0.1);
}

/* Warning Notification */
.notification-warning {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.notification-warning .notification-close:hover {
  background-color: rgba(133, 100, 4, 0.1);
}

/* Info Notification */
.notification-info {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.notification-info .notification-close:hover {
  background-color: rgba(12, 84, 96, 0.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .notification {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
  
  .notification-content {
    padding: 12px;
  }
  
  .notification-message {
    font-size: 13px;
  }
}

/* Animation for multiple notifications */
.notification:nth-child(2) {
  top: 80px;
}

.notification:nth-child(3) {
  top: 140px;
}

.notification:nth-child(4) {
  top: 200px;
}

@media (max-width: 768px) {
  .notification:nth-child(2) {
    top: 70px;
  }
  
  .notification:nth-child(3) {
    top: 130px;
  }
  
  .notification:nth-child(4) {
    top: 190px;
  }
}
