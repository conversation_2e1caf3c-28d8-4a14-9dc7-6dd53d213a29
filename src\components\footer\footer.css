/* Footer Styles */
.footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  margin-top: auto;
}

.footer-content {
  padding: clamp(40px, 8vw, 60px) 0 clamp(20px, 4vw, 30px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(20px, 5vw, 40px);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: clamp(2rem, 4vw, 3rem);
}

/* Footer Sections */
.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: clamp(1.5rem, 3vw, 1.8rem);
  font-weight: 700;
  margin-bottom: 1rem;
  color: #f39c12;
  line-height: 1.2;
}

.footer-subtitle {
  font-size: clamp(1.1rem, 2.2vw, 1.2rem);
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ecf0f1;
  line-height: 1.2;
}

.footer-description {
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  line-height: 1.6;
  color: #ffffff;
  margin-bottom: 1.5rem;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  background: linear-gradient(135deg, #e67e22, #f39c12);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(230, 126, 34, 0.3);
}

.social-icon {
  font-size: 1.2rem;
}

/* Footer Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.8rem;
}

.footer-links a {
  color: #ffffff;
  text-decoration: none;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  transition: all 0.3s ease;
  display: inline-block;
  position: relative;
}

.footer-links a:hover {
  color: #f39c12;
  transform: translateX(5px);
}

.footer-links a::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #e67e22, #f39c12);
  transition: width 0.3s ease;
}

.footer-links a:hover::before {
  width: 100%;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.8rem;
}

.contact-icon {
  font-size: 1.1rem;
  color: #f39c12;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.contact-text {
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  color: #ffffff;
  line-height: 1.5;
}

/* Opening Hours */
.hours-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.hour-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: clamp(0.85rem, 1.6vw, 0.9rem);
}

.hour-item:last-child {
  border-bottom: none;
}

.day {
  color: #ffffff;
  font-weight: 500;
}

.time {
  color: #f39c12;
  font-weight: 600;
}

/* Newsletter Section */
.newsletter-section {
  background: rgba(255, 255, 255, 0.05);
  padding: clamp(1.5rem, 3vw, 2rem);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.newsletter-text {
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  color: #ffffff;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.newsletter-form {
  width: 100%;
}

.newsletter-input-group {
  display: flex;
  gap: 0.5rem;
  flex-direction: column;
}

.newsletter-input {
  flex: 1;
  padding: clamp(0.8rem, 1.5vw, 1rem);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.newsletter-input:focus {
  outline: none;
  border-color: #f39c12;
  box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.2);
}

.newsletter-btn {
  padding: clamp(0.8rem, 1.5vw, 1rem) clamp(1.5rem, 3vw, 2rem);
  background: linear-gradient(135deg, #e67e22, #f39c12);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(230, 126, 34, 0.3);
}

/* Footer Bottom */
.footer-bottom {
  background: rgba(0, 0, 0, 0.2);
  padding: clamp(1rem, 2vw, 1.5rem) 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright p {
  font-size: clamp(0.85rem, 1.6vw, 0.9rem);
  color: #ffffff;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: clamp(1rem, 2vw, 1.5rem);
  flex-wrap: wrap;
}

.footer-bottom-links a {
  color: #ffffff;
  text-decoration: none;
  font-size: clamp(0.85rem, 1.6vw, 0.9rem);
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #f39c12;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .newsletter-input-group {
    flex-direction: column;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-bottom-links {
    justify-content: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hour-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }

  .contact-item {
    flex-direction: column;
    gap: 0.3rem;
  }

  .footer-bottom-links {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Responsive Grid Adjustments */
@media (min-width: 1200px) {
  .footer-grid {
    grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr 1.5fr;
  }
}

@media (max-width: 1199px) and (min-width: 769px) {
  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
