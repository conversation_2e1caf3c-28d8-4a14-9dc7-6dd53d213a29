/* About Page Styles */
.about-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(20px, 5vw, 40px);
}

/* Hero Section */
.about-hero {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: clamp(2rem, 5vw, 4rem);
  align-items: center;
  min-height: 70vh;
  padding: clamp(40px, 8vw, 80px) clamp(20px, 5vw, 40px);
  background: linear-gradient(135deg, #e67e22 0%, #f39c12 100%);
  color: white;
}

.hero-content h1 {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-content p {
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  opacity: 0.95;
  line-height: 1.6;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.placeholder-image {
  font-size: clamp(8rem, 15vw, 12rem);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: clamp(2rem, 4vw, 3rem);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Our Story Section */
.our-story {
  padding: clamp(60px, 10vw, 100px) 0;
}

.story-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: clamp(3rem, 6vw, 5rem);
  align-items: center;
}

.story-text h2 {
  font-size: clamp(2rem, 4vw, 2.8rem);
  color: #2c3e50;
  margin-bottom: 2rem;
  font-weight: 600;
}

.story-text p {
  font-size: clamp(1rem, 2vw, 1.1rem);
  line-height: 1.8;
  color: #555;
  margin-bottom: 1.5rem;
}

.story-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.story-image .placeholder-image {
  font-size: clamp(6rem, 12vw, 8rem);
  background: linear-gradient(135deg, #e67e22, #f39c12);
  color: white;
  border-radius: 15px;
  padding: clamp(1.5rem, 3vw, 2rem);
}

/* Values Section */
.values-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: clamp(60px, 10vw, 100px) 0;
}

.values-section h2 {
  text-align: center;
  font-size: clamp(2rem, 4vw, 2.8rem);
  color: #2c3e50;
  margin-bottom: clamp(3rem, 6vw, 4rem);
  font-weight: 600;
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: clamp(2rem, 4vw, 3rem);
}

.value-card {
  background: white;
  padding: clamp(2rem, 4vw, 2.5rem);
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.value-icon {
  font-size: clamp(3rem, 6vw, 4rem);
  margin-bottom: 1rem;
}

.value-card h3 {
  font-size: clamp(1.3rem, 2.5vw, 1.5rem);
  color: #2c3e50;
  margin-bottom: 1rem;
  font-weight: 600;
}

.value-card p {
  font-size: clamp(0.95rem, 1.8vw, 1rem);
  color: #666;
  line-height: 1.6;
}

/* Team Section */
.team-section {
  padding: clamp(60px, 10vw, 100px) 0;
}

.team-section h2 {
  text-align: center;
  font-size: clamp(2rem, 4vw, 2.8rem);
  color: #2c3e50;
  margin-bottom: clamp(3rem, 6vw, 4rem);
  font-weight: 600;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: clamp(2rem, 4vw, 3rem);
}

.team-member {
  background: white;
  padding: clamp(2rem, 4vw, 2.5rem);
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
}

.member-image {
  font-size: clamp(4rem, 8vw, 5rem);
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #e67e22, #f39c12);
  color: white;
  width: clamp(80px, 15vw, 100px);
  height: clamp(80px, 15vw, 100px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.team-member h3 {
  font-size: clamp(1.3rem, 2.5vw, 1.5rem);
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.team-member p:first-of-type {
  color: #e67e22;
  font-weight: 500;
  margin-bottom: 1rem;
  font-size: clamp(0.95rem, 1.8vw, 1rem);
}

.team-member p:last-of-type {
  color: #666;
  line-height: 1.6;
  font-size: clamp(0.9rem, 1.7vw, 0.95rem);
}

/* Mission Section */
.mission-section {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: clamp(60px, 10vw, 100px) 0;
}

.mission-content h2 {
  text-align: center;
  font-size: clamp(2rem, 4vw, 2.8rem);
  margin-bottom: 2rem;
  font-weight: 600;
}

.mission-content p {
  text-align: center;
  font-size: clamp(1.1rem, 2.2vw, 1.3rem);
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto 3rem;
  opacity: 0.95;
}

.mission-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: clamp(2rem, 4vw, 3rem);
  margin-top: 3rem;
}

.stat {
  text-align: center;
  padding: clamp(1.5rem, 3vw, 2rem);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat h3 {
  font-size: clamp(2rem, 4vw, 2.5rem);
  color: #f39c12;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.stat p {
  font-size: clamp(1rem, 2vw, 1.1rem);
  opacity: 0.9;
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .about-hero {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .story-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 2rem;
  }

  .values-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    grid-template-columns: 1fr;
  }

  .mission-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .mission-stats {
    grid-template-columns: 1fr;
  }
}
