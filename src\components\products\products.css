/* Products Section */
.products-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: clamp(2rem, 5vw, 4rem) clamp(1rem, 3vw, 2rem);
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #fff8f0 0%, rgba(255, 159, 28, 0.05) 100%);
  min-height: 100vh;
}

.products-header {
  text-align: center;
  margin-bottom: clamp(2rem, 4vw, 3rem);
}

.products-header h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  color: #2c3e50;
  margin-bottom: clamp(0.5rem, 2vw, 1rem);
  font-weight: 700;
  background: linear-gradient(135deg, #ff9f1c 0%, #ffb54f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.products-header p {
  font-size: clamp(1rem, 2.5vw, 1.3rem);
  color: #666;
  margin: 0;
  font-weight: 400;
}

/* Controls */
.products-controls {
  margin-bottom: 40px;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 12px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s;
}

.search-input:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.category-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: clamp(0.5rem, 2vw, 1rem);
  padding: 0 1rem;
}

.filter-btn {
  padding: clamp(0.6rem, 2vw, 0.8rem) clamp(1rem, 3vw, 1.5rem);
  border: 2px solid rgba(255, 159, 28, 0.3);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  font-size: clamp(0.85rem, 2vw, 0.95rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #2c3e50;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.filter-btn:hover::before {
  left: 100%;
}

.filter-btn:hover {
  border-color: #ff9f1c;
  color: #ff9f1c;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 159, 28, 0.2);
}

.filter-btn.active {
  background: linear-gradient(135deg, #ff9f1c 0%, #ffb54f 100%);
  border-color: #ff9f1c;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 159, 28, 0.3);
}

.filter-btn.view-more,
.filter-btn.view-less {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border-color: #2c3e50;
  color: white;
}

.filter-btn.view-more:hover,
.filter-btn.view-less:hover {
  background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
  border-color: #34495e;
}

/* No Products */
.no-products {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: clamp(300px, 40vh, 500px);
  text-align: center;
  padding: clamp(2rem, 5vw, 4rem);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 2px dashed rgba(255, 159, 28, 0.3);
  margin: clamp(2rem, 4vw, 3rem) 0;
}

.no-products h3 {
  color: #2c3e50;
  margin-bottom: clamp(0.5rem, 2vw, 1rem);
  font-size: clamp(1.3rem, 3vw, 1.8rem);
  font-weight: 700;
}

.no-products p {
  color: #666;
  margin: 0;
  font-size: clamp(0.9rem, 2vw, 1.1rem);
  line-height: 1.5;
  max-width: 500px;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(clamp(280px, 30vw, 350px), 1fr));
  gap: clamp(1.5rem, 3vw, 2.5rem);
  margin-bottom: clamp(2rem, 4vw, 3rem);
}

.product-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(255, 159, 28, 0.1);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 159, 28, 0.15);
  position: relative;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff9f1c 0%, #ffb54f 100%);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(255, 159, 28, 0.2);
  border-color: rgba(255, 159, 28, 0.3);
}

.product-image {
  height: clamp(180px, 25vw, 220px);
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.05) 0%, rgba(255, 181, 79, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.placeholder-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.08) 0%, rgba(255, 181, 79, 0.08) 100%);
  color: #ff9f1c;
}

.placeholder-image span {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  opacity: 0.7;
  margin-bottom: 0.5rem;
}

.product-info {
  padding: clamp(1rem, 3vw, 1.5rem);
}

.product-name {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 clamp(0.5rem, 2vw, 0.8rem) 0;
  line-height: 1.3;
}

.product-description {
  color: #666;
  font-size: clamp(0.85rem, 2vw, 0.95rem);
  line-height: 1.5;
  margin: 0 0 clamp(0.8rem, 2vw, 1rem) 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category-tag {
  display: inline-block;
  background: linear-gradient(135deg, rgba(255, 159, 28, 0.15) 0%, rgba(255, 181, 79, 0.15) 100%);
  color: #ff9f1c;
  padding: clamp(0.3rem, 1vw, 0.5rem) clamp(0.8rem, 2vw, 1rem);
  border-radius: 15px;
  font-size: clamp(0.75rem, 1.5vw, 0.85rem);
  font-weight: 600;
  margin-bottom: clamp(0.8rem, 2vw, 1rem);
  border: 1px solid rgba(255, 159, 28, 0.2);
}

.product-price {
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  font-weight: 700;
  color: #ff9f1c;
  margin-bottom: clamp(0.8rem, 2vw, 1rem);
  background: linear-gradient(135deg, #ff9f1c 0%, #ffb54f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.product-actions {
  padding: 0 clamp(1rem, 3vw, 1.5rem) clamp(1rem, 3vw, 1.5rem);
}

.add-to-cart-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff9f1c 0%, #ffb54f 100%);
  color: white;
  border: none;
  padding: clamp(0.8rem, 2vw, 1rem) clamp(1rem, 3vw, 1.5rem);
  border-radius: 15px;
  font-size: clamp(0.9rem, 2vw, 1rem);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.add-to-cart-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.add-to-cart-btn:hover::before {
  left: 100%;
}

.add-to-cart-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ffb54f 0%, #ff9f1c 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(255, 159, 28, 0.3);
}

.add-to-cart-btn.added {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  cursor: not-allowed;
}

.add-to-cart-btn:disabled {
  opacity: 0.8;
}

/* Quantity Controls */
.quantity-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 4px;
  border: 2px solid #e9ecef;
  width: 100%;
  max-width: 140px;
  margin: 0 auto;
}

.quantity-btn {
  background: linear-gradient(135deg, #ff9f1c 0%, #ffb54f 100%);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(255, 159, 28, 0.2);
}

.quantity-btn:hover {
  background: linear-gradient(135deg, #ffb54f 0%, #ff9f1c 100%);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(255, 159, 28, 0.3);
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn.minus {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);
}

.quantity-btn.minus:hover {
  background: linear-gradient(135deg, #e74c3c 0%, #dc3545 100%);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.quantity-display {
  font-size: 16px;
  font-weight: bold;
  color: #2c3e50;
  min-width: 32px;
  text-align: center;
  padding: 0 8px;
  background: white;
  border-radius: 12px;
  margin: 0 4px;
  line-height: 32px;
  border: 1px solid #e9ecef;
}

/* Products Summary */
.products-summary {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.products-summary p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .products-container {
    padding: clamp(1.5rem, 4vw, 2.5rem) clamp(0.8rem, 2vw, 1.5rem);
  }

  .category-filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding: 0 0.5rem 1rem;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .category-filters::-webkit-scrollbar {
    display: none;
  }

  .filter-btn {
    flex-shrink: 0;
    min-width: max-content;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fit, minmax(clamp(250px, 45vw, 300px), 1fr));
    gap: clamp(1rem, 3vw, 1.5rem);
  }

  .product-image {
    height: clamp(160px, 20vw, 180px);
  }

  .no-products {
    padding: clamp(1.5rem, 4vw, 2.5rem);
    margin: clamp(1.5rem, 3vw, 2rem) 0;
  }
}

@media (max-width: 480px) {
  .products-container {
    padding: clamp(1rem, 3vw, 2rem) clamp(0.5rem, 2vw, 1rem);
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: clamp(1rem, 2vw, 1.5rem);
  }

  .product-card {
    max-width: 100%;
  }

  .product-image {
    height: clamp(140px, 25vw, 160px);
  }

  .filter-btn {
    padding: clamp(0.5rem, 1.5vw, 0.6rem) clamp(0.8rem, 2vw, 1rem);
    font-size: clamp(0.8rem, 1.8vw, 0.85rem);
  }

  .category-filters {
    gap: clamp(0.3rem, 1vw, 0.5rem);
  }
}

/* Perfect mobile sizing with clamp */
@media (max-width: 320px) {
  .products-container {
    padding: 1rem 0.5rem;
  }

  .products-grid {
    gap: 1rem;
  }

  .product-image {
    height: 140px;
  }

  .filter-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
  }
}