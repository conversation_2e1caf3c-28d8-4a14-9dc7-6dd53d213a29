# ========================================
# SAMPLE CAFE WEBSITE - ENVIRONMENT TEMPLATE
# ========================================
# Copy this file to .env.local and fill in your actual values

# Firebase Configuration (REQUIRED)
# Get these from Firebase Console > Project Settings > General > Your apps
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Application Settings (REQUIRED)
NEXT_PUBLIC_APP_NAME="Your Cafe Name"
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Firebase Admin Setup (REQUIRED)
NEXT_PUBLIC_ENABLE_ADMIN_SETUP=true

# Development Settings (REQUIRED)
NODE_ENV=development

# Security Settings (REQUIRED - Generate secure random keys)
JWT_SECRET=your_secure_jwt_secret_key_here_32_chars_minimum
ENCRYPTION_KEY=your_secure_encryption_key_here_32_chars

# Vercel Blob Storage (REQUIRED for image uploads)
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token_here

# Legacy Admin Authentication (OPTIONAL - Backup for Firebase Auth)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here

# Business Information (OPTIONAL)
NEXT_PUBLIC_PHONE=******-YOUR-CAFE
NEXT_PUBLIC_EMAIL=<EMAIL>
NEXT_PUBLIC_ADDRESS="Your Cafe Address"

# Business Hours (OPTIONAL)
NEXT_PUBLIC_HOURS_WEEKDAY="7:00 AM - 9:00 PM"
NEXT_PUBLIC_HOURS_WEEKEND="8:00 AM - 10:00 PM"

# Social Media Links (OPTIONAL)
NEXT_PUBLIC_FACEBOOK_URL=https://facebook.com/your_page
NEXT_PUBLIC_INSTAGRAM_URL=https://instagram.com/your_account
NEXT_PUBLIC_TWITTER_URL=https://twitter.com/your_account

# Advanced Features (OPTIONAL)
# Firebase Admin SDK (for server-side operations)
FIREBASE_PRIVATE_KEY="your_private_key_here"
FIREBASE_CLIENT_EMAIL=your_service_account_email@your_project_id.iam.gserviceaccount.com

# Email Configuration (for contact forms)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id_here
