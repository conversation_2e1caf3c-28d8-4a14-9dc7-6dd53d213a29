rules_version = '2';

// Firebase Storage Security Rules for Sample Cafe Website
service firebase.storage {
  match /b/{bucket}/o {
    
    // Product images - read for everyone, write only for authenticated admins
    match /products/{productId}/{allPaths=**} {
      allow read: if true; // Public read access for product images
      allow write: if request.auth != null && isAdmin();
      allow delete: if request.auth != null && isAdmin();
    }
    
    // Category images - read for everyone, write only for authenticated admins
    match /categories/{categoryId}/{allPaths=**} {
      allow read: if true; // Public read access for category images
      allow write: if request.auth != null && isAdmin();
      allow delete: if request.auth != null && isAdmin();
    }
    
    // General uploads (banners, logos, etc.) - read for everyone, write only for authenticated admins
    match /uploads/{allPaths=**} {
      allow read: if true; // Public read access for general images
      allow write: if request.auth != null && isAdmin() && isValidImage();
      allow delete: if request.auth != null && isAdmin();
    }
    
    // Admin profile images - only accessible by the specific admin
    match /admin-profiles/{adminId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == adminId;
    }
    
    // Helper function to check if user is an admin
    function isAdmin() {
      return request.auth != null;
      // Note: In a real implementation, you'd check against the admins collection
      // This is simplified for the storage rules context
    }
    
    // Helper function to validate image uploads
    function isValidImage() {
      return request.resource.size < 5 * 1024 * 1024 && // Max 5MB
             request.resource.contentType.matches('image/.*'); // Only images
    }
    
    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
